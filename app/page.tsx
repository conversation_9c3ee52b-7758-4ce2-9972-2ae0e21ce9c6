import { Head<PERSON> } from "@/components/header";
import { <PERSON> } from "@/components/hero";
import { About } from "@/components/about";
import { Skills } from "@/components/skills";
import { Projects } from "@/components/projects";
import { Contact } from "@/components/contact";
import { CVDownload } from "@/components/cv-download";
import { EasterEggs } from "@/components/easter-eggs";
import { ScrollToTop } from "@/components/scroll-to-top";

export default function Home() {
  return (
    <main className="min-h-screen">
      <Header />
      <Hero />
      <About />
      <Skills />
      <Projects />
      <Contact />

      {/* Floating CV Download Button */}
      <CVDownload variant="floating" />

      {/* Scroll to Top Button */}
      <ScrollToTop />

      {/* Easter Eggs and Playful Elements */}
      <EasterEggs />

      {/* Footer */}
      <footer className="py-8 border-t bg-background">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-sm text-muted-foreground mb-2">
              "Le code est mon art, le futur est ma toile."
            </p>
            <p className="text-xs text-muted-foreground">
              © 2024 Taylan Ekin Kara. Built with Next.js, TypeScript, and lots of ☕
            </p>
            <p className="text-xs text-muted-foreground mt-2 opacity-60">
              🎮 Psst... try the Konami code or explore the hidden buttons!
            </p>
          </div>
        </div>
      </footer>
    </main>
  );
}
